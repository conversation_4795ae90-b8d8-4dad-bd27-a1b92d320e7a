/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/workflows/route";
exports.ids = ["app/api/workflows/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflows%2Froute&page=%2Fapi%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflows%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflows%2Froute&page=%2Fapi%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflows%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_workflows_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/workflows/route.ts */ \"(rsc)/./src/app/api/workflows/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/workflows/route\",\n        pathname: \"/api/workflows\",\n        filename: \"route\",\n        bundlePath: \"app/api/workflows/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\workflows\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_workflows_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflows%2Froute&page=%2Fapi%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflows%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/workflows/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/workflows/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_workflow_WorkflowPersistence__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/workflow/WorkflowPersistence */ \"(rsc)/./src/lib/workflow/WorkflowPersistence.ts\");\n/**\n * Workflow Management API\n * Handles CRUD operations for workflows\n */ \n\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies\n        });\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const workflowId = searchParams.get('id');\n        if (workflowId) {\n            // Get specific workflow\n            const workflow = await _lib_workflow_WorkflowPersistence__WEBPACK_IMPORTED_MODULE_3__.workflowPersistence.getWorkflow(workflowId, user.id);\n            if (!workflow) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Workflow not found'\n                }, {\n                    status: 404\n                });\n            }\n            // Get API key info\n            const apiKeyInfo = await _lib_workflow_WorkflowPersistence__WEBPACK_IMPORTED_MODULE_3__.workflowPersistence.getWorkflowAPIKey(workflowId, user.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                workflow,\n                api_key_info: apiKeyInfo ? {\n                    id: apiKeyInfo.id,\n                    key_name: apiKeyInfo.key_name,\n                    key_prefix: apiKeyInfo.key_prefix,\n                    encrypted_suffix: apiKeyInfo.encrypted_key_suffix,\n                    status: apiKeyInfo.status,\n                    total_requests: apiKeyInfo.total_requests,\n                    last_used_at: apiKeyInfo.last_used_at,\n                    created_at: apiKeyInfo.created_at\n                } : null\n            });\n        } else {\n            // Get all user workflows\n            const workflows = await _lib_workflow_WorkflowPersistence__WEBPACK_IMPORTED_MODULE_3__.workflowPersistence.getUserWorkflows(user.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                workflows: workflows.map((w)=>({\n                        id: w.id,\n                        name: w.name,\n                        description: w.description,\n                        is_active: w.is_active,\n                        version: w.version,\n                        created_at: w.created_at,\n                        updated_at: w.updated_at,\n                        node_count: w.nodes.length,\n                        edge_count: w.edges.length\n                    }))\n            });\n        }\n    } catch (error) {\n        console.error('Workflow GET error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch workflows',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies\n        });\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, description, nodes, edges, settings } = body;\n        if (!name || !nodes || !edges) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: name, nodes, edges'\n            }, {\n                status: 400\n            });\n        }\n        // Validate nodes structure\n        if (!Array.isArray(nodes) || nodes.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Nodes must be a non-empty array'\n            }, {\n                status: 400\n            });\n        }\n        // Check for User Request node (required entry point)\n        const hasUserRequestNode = nodes.some((node)=>node.type === 'userRequest');\n        if (!hasUserRequestNode) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Workflow must contain at least one User Request node'\n            }, {\n                status: 400\n            });\n        }\n        // Save workflow and generate API key\n        const { workflow, apiKey } = await _lib_workflow_WorkflowPersistence__WEBPACK_IMPORTED_MODULE_3__.workflowPersistence.saveWorkflow(user.id, name, description || '', nodes, edges, settings || {});\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            workflow: {\n                id: workflow.id,\n                name: workflow.name,\n                description: workflow.description,\n                is_active: workflow.is_active,\n                version: workflow.version,\n                created_at: workflow.created_at,\n                updated_at: workflow.updated_at\n            },\n            api_key: apiKey,\n            message: 'Workflow saved successfully. Save your API key - it will not be shown again!'\n        });\n    } catch (error) {\n        console.error('Workflow POST error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save workflow',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies\n        });\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { id, name, description, nodes, edges, settings, is_active } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Workflow ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Update workflow\n        const workflow = await _lib_workflow_WorkflowPersistence__WEBPACK_IMPORTED_MODULE_3__.workflowPersistence.updateWorkflow(id, user.id, {\n            name,\n            description,\n            nodes,\n            edges,\n            settings,\n            is_active\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            workflow: {\n                id: workflow.id,\n                name: workflow.name,\n                description: workflow.description,\n                is_active: workflow.is_active,\n                version: workflow.version,\n                updated_at: workflow.updated_at\n            }\n        });\n    } catch (error) {\n        console.error('Workflow PUT error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update workflow',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies\n        });\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const workflowId = searchParams.get('id');\n        if (!workflowId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Workflow ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Delete workflow and its API key\n        await _lib_workflow_WorkflowPersistence__WEBPACK_IMPORTED_MODULE_3__.workflowPersistence.deleteWorkflow(workflowId, user.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Workflow deleted successfully'\n        });\n    } catch (error) {\n        console.error('Workflow DELETE error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete workflow',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/workflows/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/workflow/WorkflowPersistence.ts":
/*!*************************************************!*\
  !*** ./src/lib/workflow/WorkflowPersistence.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowPersistenceService: () => (/* binding */ WorkflowPersistenceService),\n/* harmony export */   workflowPersistence: () => (/* binding */ workflowPersistence)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Workflow Persistence Service\n * Handles saving, loading, and managing workflows similar to how models work\n */ \n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nclass WorkflowPersistenceService {\n    static getInstance() {\n        if (!WorkflowPersistenceService.instance) {\n            WorkflowPersistenceService.instance = new WorkflowPersistenceService();\n        }\n        return WorkflowPersistenceService.instance;\n    }\n    /**\n   * Save a new workflow (similar to saving models)\n   */ async saveWorkflow(userId, name, description, nodes, edges, settings = {}) {\n        try {\n            // Create workflow record\n            const workflowId = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomUUID();\n            const workflowData = {\n                id: workflowId,\n                user_id: userId,\n                name,\n                description,\n                nodes,\n                edges,\n                settings,\n                is_active: true,\n                is_template: false,\n                version: 1,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            };\n            const { data: workflow, error: workflowError } = await supabase.from('manual_build_workflows').insert(workflowData).select().single();\n            if (workflowError) {\n                throw new Error(`Failed to save workflow: ${workflowError.message}`);\n            }\n            // Generate persistent API key for this workflow\n            const apiKey = await this.generateWorkflowAPIKey(workflowId, userId, name);\n            return {\n                workflow: workflow,\n                apiKey\n            };\n        } catch (error) {\n            console.error('Error saving workflow:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update an existing workflow\n   */ async updateWorkflow(workflowId, userId, updates) {\n        try {\n            // First get the current version\n            const { data: currentWorkflow } = await supabase.from('manual_build_workflows').select('version').eq('id', workflowId).eq('user_id', userId).single();\n            const updateData = {\n                ...updates,\n                updated_at: new Date().toISOString(),\n                version: (currentWorkflow?.version || 0) + 1 // Increment version\n            };\n            const { data: workflow, error } = await supabase.from('manual_build_workflows').update(updateData).eq('id', workflowId).eq('user_id', userId).select().single();\n            if (error) {\n                throw new Error(`Failed to update workflow: ${error.message}`);\n            }\n            return workflow;\n        } catch (error) {\n            console.error('Error updating workflow:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's workflows\n   */ async getUserWorkflows(userId) {\n        try {\n            const { data: workflows, error } = await supabase.from('manual_build_workflows').select('*').eq('user_id', userId).eq('is_template', false).order('updated_at', {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to fetch workflows: ${error.message}`);\n            }\n            return workflows;\n        } catch (error) {\n            console.error('Error fetching workflows:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get a specific workflow\n   */ async getWorkflow(workflowId, userId) {\n        try {\n            const { data: workflow, error } = await supabase.from('manual_build_workflows').select('*').eq('id', workflowId).eq('user_id', userId).single();\n            if (error) {\n                if (error.code === 'PGRST116') {\n                    return null; // Not found\n                }\n                throw new Error(`Failed to fetch workflow: ${error.message}`);\n            }\n            return workflow;\n        } catch (error) {\n            console.error('Error fetching workflow:', error);\n            throw error;\n        }\n    }\n    /**\n   * Delete a workflow and its API key\n   */ async deleteWorkflow(workflowId, userId) {\n        try {\n            // Delete workflow API key first\n            await supabase.from('workflow_api_keys').delete().eq('workflow_id', workflowId).eq('user_id', userId);\n            // Delete workflow\n            const { error } = await supabase.from('manual_build_workflows').delete().eq('id', workflowId).eq('user_id', userId);\n            if (error) {\n                throw new Error(`Failed to delete workflow: ${error.message}`);\n            }\n        } catch (error) {\n            console.error('Error deleting workflow:', error);\n            throw error;\n        }\n    }\n    /**\n   * Generate persistent API key for workflow\n   */ async generateWorkflowAPIKey(workflowId, userId, workflowName) {\n        try {\n            // Generate API key similar to your existing system\n            const keyPrefix = 'rk_wf'; // RouKey Workflow\n            const keyBody = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n            const fullKey = `${keyPrefix}_${keyBody}`;\n            // Hash the key for storage\n            const keyHash = crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(fullKey).digest('hex');\n            // Encrypt the suffix for partial display\n            const keySuffix = keyBody.slice(-8);\n            const encryptedSuffix = crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('md5').update(keySuffix).digest('hex');\n            // Store API key\n            const apiKeyData = {\n                id: crypto__WEBPACK_IMPORTED_MODULE_0___default().randomUUID(),\n                workflow_id: workflowId,\n                user_id: userId,\n                key_name: `${workflowName} API Key`,\n                key_prefix: keyPrefix,\n                key_hash: keyHash,\n                encrypted_key_suffix: encryptedSuffix,\n                permissions: {\n                    execute: true,\n                    read_logs: true,\n                    read_status: true\n                },\n                status: 'active',\n                total_requests: 0,\n                created_at: new Date().toISOString()\n            };\n            const { error } = await supabase.from('workflow_api_keys').insert(apiKeyData);\n            if (error) {\n                throw new Error(`Failed to create API key: ${error.message}`);\n            }\n            return fullKey;\n        } catch (error) {\n            console.error('Error generating workflow API key:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get workflow API key info (without the actual key)\n   */ async getWorkflowAPIKey(workflowId, userId) {\n        try {\n            const { data: apiKey, error } = await supabase.from('workflow_api_keys').select('*').eq('workflow_id', workflowId).eq('user_id', userId).single();\n            if (error) {\n                if (error.code === 'PGRST116') {\n                    return null; // Not found\n                }\n                throw new Error(`Failed to fetch API key: ${error.message}`);\n            }\n            return apiKey;\n        } catch (error) {\n            console.error('Error fetching workflow API key:', error);\n            throw error;\n        }\n    }\n    /**\n   * Validate workflow API key for execution\n   */ async validateWorkflowAPIKey(apiKey) {\n        try {\n            const keyHash = crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(apiKey).digest('hex');\n            const { data: keyRecord, error } = await supabase.from('workflow_api_keys').select('workflow_id, user_id, status').eq('key_hash', keyHash).eq('status', 'active').single();\n            if (error || !keyRecord) {\n                return {\n                    valid: false\n                };\n            }\n            // Update last used timestamp and increment total_requests\n            // First get current total_requests value\n            const { data: currentKey } = await supabase.from('workflow_api_keys').select('total_requests').eq('key_hash', keyHash).single();\n            const newTotalRequests = (currentKey?.total_requests || 0) + 1;\n            await supabase.from('workflow_api_keys').update({\n                last_used_at: new Date().toISOString(),\n                total_requests: newTotalRequests\n            }).eq('key_hash', keyHash);\n            return {\n                valid: true,\n                workflowId: keyRecord.workflow_id,\n                userId: keyRecord.user_id\n            };\n        } catch (error) {\n            console.error('Error validating workflow API key:', error);\n            return {\n                valid: false\n            };\n        }\n    }\n    /**\n   * Get workflow templates\n   */ async getWorkflowTemplates(category) {\n        try {\n            let query = supabase.from('manual_build_workflows').select('*').eq('is_template', true);\n            if (category) {\n                query = query.eq('template_category', category);\n            }\n            const { data: templates, error } = await query.order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to fetch templates: ${error.message}`);\n            }\n            return templates;\n        } catch (error) {\n            console.error('Error fetching workflow templates:', error);\n            throw error;\n        }\n    }\n}\nconst workflowPersistence = WorkflowPersistenceService.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/workflow/WorkflowPersistence.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fworkflows%2Froute&page=%2Fapi%2Fworkflows%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fworkflows%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();