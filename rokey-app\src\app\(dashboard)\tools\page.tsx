'use client';

import { useState, useEffect } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { 
  LinkIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  TrashIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { TOOL_DISPLAY_NAMES, TOOL_ICONS, TOOL_DESCRIPTIONS } from '@/lib/oauth/config';

interface ToolConnection {
  tool_type: string;
  display_name: string;
  icon: string;
  description: string;
  is_connected: boolean;
  connection_status: string;
  provider_user_email?: string;
  provider_user_name?: string;
  last_used_at?: string;
  connected_at?: string;
}

export default function ToolsPage() {
  const [tools, setTools] = useState<ToolConnection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectingTool, setConnectingTool] = useState<string | null>(null);
  const supabase = createSupabaseBrowserClient();

  // Fetch tool connections
  const fetchToolConnections = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/auth/tools/status');
      
      if (!response.ok) {
        throw new Error('Failed to fetch tool connections');
      }
      
      const data = await response.json();
      setTools(data.tools || []);
    } catch (error) {
      console.error('Error fetching tool connections:', error);
      setError(error instanceof Error ? error.message : 'Failed to load tools');
    } finally {
      setLoading(false);
    }
  };

  // Handle tool connection
  const handleConnect = async (toolType: string) => {
    setConnectingTool(toolType);
    setError(null);
    
    try {
      // Determine the OAuth provider based on tool type
      let provider = 'google';
      if (toolType === 'notion') {
        provider = 'notion';
      }
      
      // Get authorization URL
      const response = await fetch(`/api/auth/tools/${provider}/authorize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          toolType,
          returnUrl: '/tools'
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to initiate OAuth flow');
      }
      
      const { authUrl } = await response.json();
      
      // Open OAuth flow in new window
      const popup = window.open(
        authUrl,
        'oauth-popup',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );
      
      // Listen for OAuth completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          setConnectingTool(null);
          // Refresh tool connections
          setTimeout(() => fetchToolConnections(), 1000);
        }
      }, 1000);
      
    } catch (error) {
      console.error('Error connecting tool:', error);
      setError(error instanceof Error ? error.message : 'Failed to connect tool');
      setConnectingTool(null);
    }
  };

  // Handle tool disconnection
  const handleDisconnect = async (toolType: string) => {
    try {
      const response = await fetch(`/api/auth/tools/status?tool=${toolType}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        await fetchToolConnections();
      } else {
        throw new Error('Failed to disconnect tool');
      }
    } catch (error) {
      console.error('Error disconnecting tool:', error);
      setError(error instanceof Error ? error.message : 'Failed to disconnect tool');
    }
  };

  // Handle token refresh
  const handleRefresh = async (toolType: string) => {
    try {
      const response = await fetch('/api/auth/tools/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ toolType })
      });
      
      if (response.ok) {
        await fetchToolConnections();
      } else {
        throw new Error('Failed to refresh token');
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
      setError(error instanceof Error ? error.message : 'Failed to refresh token');
    }
  };

  // Get status color
  const getStatusColor = (tool: ToolConnection) => {
    if (tool.is_connected && tool.connection_status === 'connected') {
      return 'text-green-400';
    }
    if (tool.connection_status === 'expired' || tool.connection_status === 'revoked') {
      return 'text-red-400';
    }
    return 'text-yellow-400';
  };

  // Get status icon
  const getStatusIcon = (tool: ToolConnection) => {
    if (tool.is_connected && tool.connection_status === 'connected') {
      return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
    }
    return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />;
  };

  // Get status text
  const getStatusText = (tool: ToolConnection) => {
    if (tool.is_connected && tool.connection_status === 'connected') {
      return 'Connected';
    }
    switch (tool.connection_status) {
      case 'expired': return 'Expired';
      case 'revoked': return 'Revoked';
      case 'error': return 'Error';
      default: return 'Not Connected';
    }
  };

  useEffect(() => {
    fetchToolConnections();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#040716] text-white p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#040716] text-white p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Tool Connections</h1>
          <p className="text-gray-400">
            Manage your connected tools and services. Connect your accounts to enable AI models to use these tools in your workflows.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-900/20 border border-red-500/20 rounded-lg">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool) => (
            <div
              key={tool.tool_type}
              className="bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-gray-600 transition-colors"
            >
              {/* Tool Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{tool.icon}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      {tool.display_name}
                    </h3>
                    <p className="text-sm text-gray-400">
                      {tool.description}
                    </p>
                  </div>
                </div>
                {getStatusIcon(tool)}
              </div>

              {/* Connection Status */}
              <div className={`text-sm ${getStatusColor(tool)} mb-4`}>
                {getStatusText(tool)}
                {tool.provider_user_email && (
                  <div className="text-gray-400 mt-1">
                    {tool.provider_user_email}
                  </div>
                )}
              </div>

              {/* Connection Actions */}
              <div className="flex gap-2">
                {tool.is_connected ? (
                  <>
                    {(tool.connection_status === 'expired' || tool.connection_status === 'revoked') && (
                      <button
                        onClick={() => handleRefresh(tool.tool_type)}
                        className="flex-1 px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2"
                      >
                        <ArrowPathIcon className="h-4 w-4" />
                        Refresh
                      </button>
                    )}
                    <button
                      onClick={() => handleDisconnect(tool.tool_type)}
                      className="flex-1 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2"
                    >
                      <TrashIcon className="h-4 w-4" />
                      Disconnect
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => handleConnect(tool.tool_type)}
                    disabled={connectingTool === tool.tool_type}
                    className="w-full px-3 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2"
                  >
                    <LinkIcon className="h-4 w-4" />
                    {connectingTool === tool.tool_type ? 'Connecting...' : 'Connect'}
                  </button>
                )}
              </div>

              {/* Last Used */}
              {tool.last_used_at && (
                <div className="mt-3 text-xs text-gray-500">
                  Last used: {new Date(tool.last_used_at).toLocaleDateString()}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Help Section */}
        <div className="mt-12 p-6 bg-gray-800 border border-gray-700 rounded-lg">
          <h2 className="text-xl font-semibold text-white mb-3">How Tool Connections Work</h2>
          <div className="space-y-2 text-gray-300">
            <p>• Connect your accounts to enable AI models to use these tools in Manual Build workflows</p>
            <p>• Your credentials are securely encrypted and stored</p>
            <p>• Tokens are automatically refreshed to maintain connections</p>
            <p>• You can disconnect tools at any time to revoke access</p>
          </div>
        </div>
      </div>
    </div>
  );
}
