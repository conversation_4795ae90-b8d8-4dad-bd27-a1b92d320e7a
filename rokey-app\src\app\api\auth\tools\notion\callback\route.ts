// Notion OAuth Callback Endpoint
// Handles OAuth callback from Notion and exchanges code for tokens

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { getOAuthConfigForTool } from '@/lib/oauth/config';
import { storeOAuthTokens } from '@/lib/oauth/tokenManager';

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 NOTION OAUTH CALLBACK: Processing callback');
    
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    
    // Handle OAuth errors
    if (error) {
      console.error('🔐 NOTION OAUTH CALLBACK: OAuth error:', error);
      const errorMessage = searchParams.get('error_description') || 'Authorization failed';
      return NextResponse.redirect(
        new URL(`/manual-build?error=${encodeURIComponent(errorMessage)}`, request.url)
      );
    }
    
    if (!code || !state) {
      console.error('🔐 NOTION OAUTH CALLBACK: Missing code or state');
      return NextResponse.redirect(
        new URL('/manual-build?error=Invalid callback parameters', request.url)
      );
    }
    
    console.log('🔐 NOTION OAUTH CALLBACK: Code and state received');
    
    // Verify state and get user info
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: stateData, error: stateError } = await supabase
      .from('oauth_states')
      .select('*')
      .eq('state', state)
      .gt('expires_at', new Date().toISOString())
      .single();
    
    if (stateError || !stateData) {
      console.error('🔐 NOTION OAUTH CALLBACK: Invalid or expired state:', stateError);
      return NextResponse.redirect(
        new URL('/manual-build?error=Invalid or expired authorization state', request.url)
      );
    }
    
    const { user_id: userId, return_url: returnUrl } = stateData;
    console.log(`🔐 NOTION OAUTH CALLBACK: Processing Notion for user ${userId}`);
    
    // Get OAuth configuration
    const config = getOAuthConfigForTool('notion');
    if (!config) {
      console.error('🔐 NOTION OAUTH CALLBACK: OAuth config not found for Notion');
      return NextResponse.redirect(
        new URL('/manual-build?error=OAuth configuration error', request.url)
      );
    }
    
    // Exchange code for tokens (Notion uses Basic Auth)
    console.log('🔐 NOTION OAUTH CALLBACK: Exchanging code for tokens');
    const credentials = Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64');
    
    const tokenResponse = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        code,
        redirect_uri: config.redirectUri,
      }),
    });
    
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('🔐 NOTION OAUTH CALLBACK: Token exchange failed:', errorText);
      return NextResponse.redirect(
        new URL('/manual-build?error=Token exchange failed', request.url)
      );
    }
    
    const tokenData = await tokenResponse.json();
    console.log('🔐 NOTION OAUTH CALLBACK: Tokens received successfully');
    
    // Extract user/workspace info from Notion response
    const userInfo = {
      id: tokenData.bot_id,
      email: tokenData.owner?.user?.person?.email || tokenData.owner?.user?.email,
      name: tokenData.owner?.user?.name || tokenData.workspace_name,
    };
    
    // Store tokens securely
    const storedTokens = await storeOAuthTokens(userId, 'notion', {
      access_token: tokenData.access_token,
      // Notion doesn't provide refresh tokens
      provider_user_id: tokenData.bot_id,
      provider_user_email: userInfo.email,
      provider_user_name: userInfo.name || tokenData.workspace_name,
    });
    
    if (!storedTokens) {
      console.error('🔐 NOTION OAUTH CALLBACK: Failed to store tokens');
      return NextResponse.redirect(
        new URL('/manual-build?error=Failed to store authorization', request.url)
      );
    }
    
    console.log('🔐 NOTION OAUTH CALLBACK: Tokens stored successfully');
    
    // Clean up state
    await supabase
      .from('oauth_states')
      .delete()
      .eq('state', state);
    
    // Redirect back to the application
    const redirectUrl = returnUrl || '/manual-build';
    const successUrl = `${redirectUrl}?success=Notion connected successfully&tool=notion`;
    
    console.log('🔐 NOTION OAUTH CALLBACK: Redirecting to:', successUrl);
    return NextResponse.redirect(new URL(successUrl, request.url));
    
  } catch (error) {
    console.error('🔐 NOTION OAUTH CALLBACK: Unexpected error:', error);
    return NextResponse.redirect(
      new URL('/manual-build?error=OAuth callback failed', request.url)
    );
  }
}
