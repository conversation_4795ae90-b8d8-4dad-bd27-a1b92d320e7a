"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction WrenchScrewdriverIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n    }));\n}\n_c = WrenchScrewdriverIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(WrenchScrewdriverIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"WrenchScrewdriverIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/WrenchScrewdriverIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/WrenchScrewdriverIcon.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction WrenchScrewdriverIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M12 6.75a5.25 5.25 0 0 1 6.775-*********** 0 0 1 .313 1.248l-3.32 3.319c.063.475.276.934.641 1.299.365.365.824.578 1.3.64l3.318-3.319a.75.75 0 0 1 1.248.313 5.25 5.25 0 0 1-5.472 6.756c-1.018-.086-1.87.1-2.309.634L7.344 21.3A3.298 3.298 0 1 1 2.7 16.657l8.684-7.151c.533-.44.72-1.291.634-2.309A5.342 5.342 0 0 1 12 6.75ZM4.117 19.125a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Z\",\n        clipRule: \"evenodd\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"m10.076 8.64-2.201-2.2V4.874a.75.75 0 0 0-.364-.643l-3.75-2.25a.75.75 0 0 0-.916.113l-.75.75a.75.75 0 0 0-.113.916l2.25 3.75a.75.75 0 0 0 .643.364h1.564l2.062 2.062 1.575-1.297Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"m12.556 17.329 4.183 4.182a3.375 3.375 0 0 0 4.773-4.773l-3.306-3.305a6.803 6.803 0 0 1-1.53.043c-.394-.034-.682-.006-.867.042a.589.589 0 0 0-.167.063l-3.086 3.748Zm3.414-1.36a.75.75 0 0 1 1.06 0l1.875 1.876a.75.75 0 1 1-1.06 1.06L15.97 17.03a.75.75 0 0 1 0-1.06Z\",\n        clipRule: \"evenodd\"\n    }));\n}\n_c = WrenchScrewdriverIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(WrenchScrewdriverIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"WrenchScrewdriverIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/WrenchScrewdriverIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"70344f404017\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcwMzQ0ZjQwNDAxN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,CubeIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useRoutePrefetch */ \"(app-pages-browser)/./src/hooks/useRoutePrefetch.ts\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/usePredictiveNavigation */ \"(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        href: \"/dashboard\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        description: \"Overview & analytics\"\n    },\n    {\n        href: \"/my-models\",\n        label: \"My Models\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        description: \"API key management\"\n    },\n    {\n        href: \"/playground\",\n        label: \"Playground\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        description: \"Test your models\"\n    },\n    {\n        href: \"/routing-setup\",\n        label: \"Routing Setup\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        description: \"Configure routing\"\n    },\n    {\n        href: \"/manual-build\",\n        label: \"Manual Build\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        description: \"Visual workflow builder\"\n    },\n    {\n        href: \"/tools\",\n        label: \"Tool Connections\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        description: \"Manage connected tools\"\n    },\n    {\n        href: \"/logs\",\n        label: \"Logs\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        description: \"Request history\"\n    },\n    {\n        href: \"/training\",\n        label: \"Training\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        description: \"AI training & knowledge\"\n    },\n    {\n        href: \"/analytics\",\n        label: \"Analytics\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_CubeIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_WrenchScrewdriverIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n        description: \"Advanced insights\"\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { isCollapsed, isHovered, isHoverDisabled, setHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar)();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe)();\n    const { navigateOptimistically } = navigationContext || {\n        navigateOptimistically: ()=>{}\n    };\n    const { prefetchOnHover } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch)();\n    const { prefetchWhenIdle } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch)();\n    const { prefetchChatHistory } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    const { predictions, isLearning } = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation)();\n    const contextualSuggestions = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions)();\n    // Enhanced prefetching with predictive navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const allRoutes = navItems.map({\n                \"Sidebar.useEffect.allRoutes\": (item)=>item.href\n            }[\"Sidebar.useEffect.allRoutes\"]);\n            // Combine predictive routes with standard prefetching\n            const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions\n            const contextualRoutes = contextualSuggestions.filter({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.priority === 'high'\n            }[\"Sidebar.useEffect.contextualRoutes\"]).map({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.route\n            }[\"Sidebar.useEffect.contextualRoutes\"]).slice(0, 2);\n            const routesToPrefetch = [\n                ...predictiveRoutes,\n                ...contextualRoutes,\n                ...allRoutes.filter({\n                    \"Sidebar.useEffect.routesToPrefetch\": (route)=>route !== pathname && !predictiveRoutes.includes(route) && !contextualRoutes.includes(route)\n                }[\"Sidebar.useEffect.routesToPrefetch\"]),\n                '/playground',\n                '/logs'\n            ].slice(0, 6); // Increased limit for better coverage\n            console.log(\"\\uD83E\\uDDE0 [PREDICTIVE] Prefetching routes:\", {\n                predictive: predictiveRoutes,\n                contextual: contextualRoutes,\n                total: routesToPrefetch,\n                isLearning\n            });\n            const cleanup = prefetchWhenIdle(routesToPrefetch);\n            return cleanup;\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        prefetchWhenIdle,\n        predictions,\n        contextualSuggestions,\n        isLearning\n    ]);\n    // Determine if sidebar should be expanded (hover or not collapsed)\n    const isExpanded = !isCollapsed || isHovered;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] relative \".concat(isExpanded ? 'w-64' : 'w-16'),\n        onMouseEnter: ()=>!isHoverDisabled && setHovered(true),\n        onMouseLeave: ()=>!isHoverDisabled && setHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 h-full w-px bg-gradient-to-b from-gray-500/30 via-gray-400/40 to-gray-500/30\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 h-full w-0.5 bg-gray-400/15 blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 transition-all duration-200 ease-out \".concat(isExpanded ? 'px-6' : 'px-3'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 pt-4 transition-all duration-200 ease-out \".concat(isExpanded ? '' : 'text-center'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-0 scale-75 -translate-y-2' : 'opacity-100 scale-100 translate-y-0', \" \").concat(isExpanded ? 'absolute' : 'relative', \" w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/roukey_logo.png\",\n                                            alt: \"RouKey\",\n                                            width: 28,\n                                            height: 28,\n                                            className: \"object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-75 translate-y-2', \" \").concat(isExpanded ? 'relative' : 'absolute top-0 left-0 w-full'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-white tracking-tight whitespace-nowrap\",\n                                                children: \"RouKey\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400 mt-1 whitespace-nowrap\",\n                                                children: \"Smart LLM Router\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"space-y-2\",\n                            children: navItems.map((item)=>{\n                                const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                                const Icon = isActive ? item.iconSolid : item.icon;\n                                const isPredicted = predictions.includes(item.href);\n                                const contextualSuggestion = contextualSuggestions.find((s)=>s.route === item.href);\n                                // Enhanced prefetch for playground to include chat history\n                                const handlePlaygroundHover = ()=>{\n                                    if (item.href === '/playground') {\n                                        // Prefetch route\n                                        prefetchOnHover(item.href, 50).onMouseEnter();\n                                        // Also prefetch chat history for current config if available\n                                        const currentConfigId = new URLSearchParams(window.location.search).get('config');\n                                        if (currentConfigId) {\n                                            prefetchChatHistory(currentConfigId);\n                                        }\n                                    }\n                                };\n                                const hoverProps = item.href === '/playground' ? {\n                                    onMouseEnter: handlePlaygroundHover\n                                } : prefetchOnHover(item.href, 50);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        navigateOptimistically(item.href);\n                                    },\n                                    className: \"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left \".concat(isActive ? 'active' : '', \" \").concat(isExpanded ? '' : 'collapsed'),\n                                    title: isExpanded ? undefined : item.label,\n                                    ...hoverProps,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center w-full overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center transition-all duration-200 ease-out \".concat(isExpanded ? 'w-5 h-5 mr-3' : 'w-10 h-10 rounded-xl', \" \").concat(!isExpanded && isActive ? 'bg-white shadow-sm' : !isExpanded ? 'bg-transparent hover:bg-white/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'h-5 w-5' : 'h-5 w-5', \" \").concat(isActive ? 'text-orange-500' : 'text-white')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isPredicted && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out \".concat(isExpanded ? '-top-1 -right-1 w-2 h-2' : '-top-1 -right-1 w-3 h-3'),\n                                                        title: \"Predicted next destination\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 translate-x-0 max-w-full' : 'opacity-0 translate-x-4 max-w-0'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between whitespace-nowrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            contextualSuggestion && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-1.5 py-0.5 rounded-full ml-2 \".concat(contextualSuggestion.priority === 'high' ? 'bg-blue-500/20 text-blue-300' : 'bg-gray-500/20 text-gray-300'),\n                                                                children: contextualSuggestion.priority === 'high' ? '!' : '·'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs transition-colors duration-200 whitespace-nowrap \".concat(isActive ? 'text-orange-400' : 'text-gray-400'),\n                                                        children: contextualSuggestion ? contextualSuggestion.reason : item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"4fmIjYR/bVR+uFRRDpXMUCW8BlQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});