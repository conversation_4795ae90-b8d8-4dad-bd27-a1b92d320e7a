// Google OAuth Callback Endpoint
// Handles OAuth callback from Google and exchanges code for tokens

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { getOAuthConfigForTool } from '@/lib/oauth/config';
import { storeOAuthTokens } from '@/lib/oauth/tokenManager';

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 GOOGLE OAUTH CALLBACK: Processing callback');
    
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    
    // Handle OAuth errors
    if (error) {
      console.error('🔐 GOOGLE OAUTH CALLBACK: OAuth error:', error);
      const errorMessage = searchParams.get('error_description') || 'Authorization failed';
      return NextResponse.redirect(
        new URL(`/manual-build?error=${encodeURIComponent(errorMessage)}`, request.url)
      );
    }
    
    if (!code || !state) {
      console.error('🔐 GOOGLE OAUTH CALLBACK: Missing code or state');
      return NextResponse.redirect(
        new URL('/manual-build?error=Invalid callback parameters', request.url)
      );
    }
    
    console.log('🔐 GOOGLE OAUTH CALLBACK: Code and state received');
    
    // Verify state and get user info
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: stateData, error: stateError } = await supabase
      .from('oauth_states')
      .select('*')
      .eq('state', state)
      .gt('expires_at', new Date().toISOString())
      .single();
    
    if (stateError || !stateData) {
      console.error('🔐 GOOGLE OAUTH CALLBACK: Invalid or expired state:', stateError);
      return NextResponse.redirect(
        new URL('/manual-build?error=Invalid or expired authorization state', request.url)
      );
    }
    
    const { user_id: userId, tool_type: toolType, return_url: returnUrl } = stateData;
    console.log(`🔐 GOOGLE OAUTH CALLBACK: Processing ${toolType} for user ${userId}`);
    
    // Get OAuth configuration
    const config = getOAuthConfigForTool(toolType);
    if (!config) {
      console.error('🔐 GOOGLE OAUTH CALLBACK: OAuth config not found for tool:', toolType);
      return NextResponse.redirect(
        new URL('/manual-build?error=OAuth configuration error', request.url)
      );
    }
    
    // Exchange code for tokens
    console.log('🔐 GOOGLE OAUTH CALLBACK: Exchanging code for tokens');
    const tokenResponse = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: config.redirectUri,
        client_id: config.clientId,
        client_secret: config.clientSecret,
      }),
    });
    
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('🔐 GOOGLE OAUTH CALLBACK: Token exchange failed:', errorText);
      return NextResponse.redirect(
        new URL('/manual-build?error=Token exchange failed', request.url)
      );
    }
    
    const tokenData = await tokenResponse.json();
    console.log('🔐 GOOGLE OAUTH CALLBACK: Tokens received successfully');
    
    // Get user info from Google
    let userInfo = null;
    try {
      const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${tokenData.access_token}`,
        },
      });
      
      if (userInfoResponse.ok) {
        userInfo = await userInfoResponse.json();
        console.log('🔐 GOOGLE OAUTH CALLBACK: User info retrieved');
      }
    } catch (userInfoError) {
      console.warn('🔐 GOOGLE OAUTH CALLBACK: Failed to get user info:', userInfoError);
    }
    
    // Store tokens securely
    const storedTokens = await storeOAuthTokens(userId, toolType, {
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      expires_in: tokenData.expires_in,
      scope: tokenData.scope,
      provider_user_id: userInfo?.id,
      provider_user_email: userInfo?.email,
      provider_user_name: userInfo?.name,
    });
    
    if (!storedTokens) {
      console.error('🔐 GOOGLE OAUTH CALLBACK: Failed to store tokens');
      return NextResponse.redirect(
        new URL('/manual-build?error=Failed to store authorization', request.url)
      );
    }
    
    console.log('🔐 GOOGLE OAUTH CALLBACK: Tokens stored successfully');
    
    // Clean up state
    await supabase
      .from('oauth_states')
      .delete()
      .eq('state', state);
    
    // Redirect back to the application
    const redirectUrl = returnUrl || '/manual-build';
    const successUrl = `${redirectUrl}?success=Tool connected successfully&tool=${toolType}`;
    
    console.log('🔐 GOOGLE OAUTH CALLBACK: Redirecting to:', successUrl);
    return NextResponse.redirect(new URL(successUrl, request.url));
    
  } catch (error) {
    console.error('🔐 GOOGLE OAUTH CALLBACK: Unexpected error:', error);
    return NextResponse.redirect(
      new URL('/manual-build?error=OAuth callback failed', request.url)
    );
  }
}
